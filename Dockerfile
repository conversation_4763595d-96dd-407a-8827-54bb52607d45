FROM registry.api.weibo.com/wbsearch/python3.11.1

<NAME_EMAIL>

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

RUN apt-get update && apt-get install -y cron && service cron start
COPY requirements.txt .
RUN pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple

WORKDIR /data1/minisearch/upload/LLM_TASK/wis_search_flow

# 暴露端口
EXPOSE 16868

# # 启动命令（生产环境）
CMD ["sh", "start.sh"]