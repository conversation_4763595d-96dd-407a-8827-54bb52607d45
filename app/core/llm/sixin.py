
from jinja2 import Template
import re
import time
from typing import Optional

from app.core.api.model_api import Model<PERSON>wen
from lib.safe_logger import get_logger


class TopicExtractor:
    """话题提取器"""

    @staticmethod
    def extract_topic(text: str) -> str:
        """
        从文本中提取话题

        Args:
            text: 包含话题标签的文本

        Returns:
            str: 提取的话题，如果没有找到则返回空字符串
        """
        if not text:
            return ''

        pattern = r"<topic>(.*?)</topic>"
        match = re.search(pattern, text)
        if match:
            return match.group(1).strip()
        return ''

    @staticmethod
    def generate_prompt(nick_name: str, publish_time: str, blog_content: str) -> str:
        """
        生成话题提取的提示词

        Args:
            nick_name: 用户昵称
            publish_time: 发布时间
            blog_content: 博客内容

        Returns:
            str: 生成的提示词
        """
        prompt_template = Template("""
现在有一篇由{{ nick_name }}在{{ publish_time }}发布的原博，内容如下：
<blog_content>
{{ blog_content }}
</blog_content>
仔细阅读原博内容，识别核心话题，并提取出一个话题词。
请注意：
- 话题应为具体事件。
- 如果原博中不存在话题，则话题词为空。
请按照以下格式输出话题词：
<topic>...</topic>
""")
        return prompt_template.render(
            nick_name=nick_name,
            publish_time=publish_time,
            blog_content=blog_content,
        )

class SiXin:
    """思辨分析服务"""

    def __init__(self):
        self.logger = get_logger("log/sixin.log", "SiXin")
        self.topic_extractor = TopicExtractor()

    async def call(self, prompt: str) -> str:
        """
        调用思辨分析服务

        Args:
            prompt: 输入的提示词

        Returns:
            str: 处理后的结果

        Raises:
            Exception: 当调用失败时抛出异常
        """
        if not prompt or not prompt.strip():
            self.logger.warning("输入的prompt为空")
            return self._generate_default_response("")

        try:
            self.logger.info(f"开始调用思辨分析，prompt长度: {len(prompt)}")
            start_time = time.time()

            # 调用模型API
            wrapper = ModelQwen('sixin_service', {})
            raw_result = await wrapper.async_call(prompt.strip())

            # 处理结果
            processed_result = self._handle_result(raw_result)

            cost_time = time.time() - start_time
            self.logger.info(f"思辨分析调用成功，耗时: {cost_time:.3f}s")

            return processed_result

        except Exception as e:
            self.logger.error(f"思辨分析调用失败: {str(e)}")
            # 返回默认响应而不是抛出异常，保证服务可用性
            return self._generate_default_response("")

    def _handle_result(self, result: Optional[str]) -> str:
        """
        处理模型返回的结果

        Args:
            result: 模型返回的原始结果

        Returns:
            str: 处理后的结果
        """
        if not result:
            self.logger.warning("模型返回结果为空")
            return self._generate_default_response("")

        # 提取话题
        topic = self.topic_extractor.extract_topic(result)

        # 清理话题格式
        topic = self._clean_topic(topic)

        # 生成最终响应
        return self._generate_default_response(topic)

    def _clean_topic(self, topic: str) -> str:
        """
        清理话题格式

        Args:
            topic: 原始话题

        Returns:
            str: 清理后的话题
        """
        if not topic:
            return ""

        # 移除首尾的#号
        if topic.startswith('#') and topic.endswith('#'):
            topic = topic[1:-1]

        # 过滤无效话题
        if topic in ["...", "空", "无", "None", "null"]:
            topic = ""

        return topic.strip()

    def _generate_default_response(self, topic: str) -> str:
        """
        生成默认响应

        Args:
            topic: 话题名称

        Returns:
            str: 格式化的响应文本
        """
        if topic:
            return f'稍等一下，正在为你整理{topic}的完整分析。'
        else:
            return '稍等一下，正在为你整理原博的完整分析。'