
from jinja2 import Template
import re
import time

from app.core.api.model_api import ModelQwen

mids = []
urls = []
blog_contents = []
comments = []
contents = []

def get_topic(text ):
    pattern = r"<topic>(.*?)</topic>"
    match = re.search(pattern, text)
    if match:
        return match.group(1)
    else:
        return ''

def get_prompt(nick_name, publish_time, blog_content ):
    prompt_template = Template("""
现在有一篇由{{ nick_name }}在{{ publish_time }}发布的原博，内容如下：
<blog_content>
{{ blog_content }}
</blog_content>
仔细阅读原博内容，识别核心话题，并提取出一个话题词。
请注意：
- 话题应为具体事件。
- 如果原博中不存在话题，则话题词为空。
请按照以下格式输出话题词：
<topic>...</topic>
""")
    return prompt_template.render(
        nick_name=nick_name,
        publish_time=publish_time,
        blog_content=blog_content,
    )

class SiXin:

    def __init__(self):
        pass

    async def call(self, prompt):
        wrapper = ModelQwen('test', {})
        result = await wrapper.async_call(prompt)
        result = self.handle_result(result)
        return result

    def handle_result(self,result):
        def _get_topic(text) :
            pattern = r"<topic>(.*?)</topic>"
            match = re.search(pattern, text)
            if match :
                return match.group(1)
            else :
                return ''

        result = _get_topic(result)
        if result.startswith('#') and result.endswith('#') :
            # print(content)
            result = result[1 :-1]
        if result in ["...", "空"] :
            result = ''
        if result :
            result = f'稍等一下，正在为你整理{result}的完整分析。'
        else :
            result = f'稍等一下，正在为你整理原博的完整分析。'
        return result


