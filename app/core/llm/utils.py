"""榜单的公共方法"""
# -*- coding:utf-8 -*-
import json
import time
import asyncio
import aiohttp
from lib.safe_logger import get_logger
from lib.redis_utils import async_redis_client
from app.core.decorator import calculate_async_execution_time


RANKING_DATA_KEY = "wis_search_flow_ranking_data"
redis_client = async_redis_client.get_redis_server(RANKING_DATA_KEY)
logger = get_logger("log/wis_stream_query.log", "WIS_STREAM_QUERY")


async def fetch_and_filter(session, query, check_url):
    """
    调用接口判断 query 是否应该保留
    """
    try:
        async with session.get(f"{check_url}?query={query}") as resp:
            if resp.status == 200:
                data = await resp.json()
                should_keep = data.get("key", "") == "normal"
                return query, should_keep
            else:
                return query, False  # 出错默认丢弃
    except Exception as e:
        logger.error(f"Error checking query {query}: {e}")
        return query, False


@calculate_async_execution_time
async def filter_sensitive_queries(query_list, max_concurrency=100):
    """
    并发调用接口过滤 query_list，返回保留的 query 列表
    """
    check_url = "http://admin.ai.s.weibo.com/api/llm/query_apply_process.json"
    semaphore = asyncio.Semaphore(max_concurrency)
    async with aiohttp.ClientSession() as session:
        tasks = []
        for query in query_list:
            async def sem_task(q=query):
                async with semaphore:
                    return await fetch_and_filter(session, q, check_url)

            tasks.append(sem_task())

        results = await asyncio.gather(*tasks)
        return [q for q, keep in results if keep]


async def get_filter_sensitive_data(data):
    """获取过滤敏感后的数据"""
    querys = [x["query"] for x in data]
    # 过滤风控
    sensitive_filtered_queries = await filter_sensitive_queries(querys)
    sensitive_deleted_queries = list(set(querys) - set(sensitive_filtered_queries))
    logger.info(f"get_filtered_queries | sensitive_deleted_queries: {sensitive_deleted_queries}")

    result = []
    for item in data:
        if item["query"] in sensitive_filtered_queries:
            result.append(item)
    return result


@calculate_async_execution_time
async def get_ranking_key_data(key, retry=2):
    """
    通过key获取排名数据
    """
    redis_key = f"{RANKING_DATA_KEY}_{key}"
    for _ in range(retry):
        try:
            ranking_data = await redis_client.get(redis_key)
            if ranking_data:
                ranking_data = json.loads(ranking_data)
                return ranking_data["data"]
            return []
        except Exception as e:
            logger.error(f"获取排名数据失败，错误信息：{e}")
    logger.error("获取排名数据失败，重试次数已用尽")
    return []

async def set_ranking_key_data(key, data, retry=2):
    """
    通过key设置排名数据
    """
    redis_key = f"{RANKING_DATA_KEY}_{key}"
    for _ in range(retry):
        try:
            await redis_client.set(redis_key, json.dumps({"data": data}))
            return True
        except Exception as e:
            logger.error(f"设置排名数据失败，错误信息：{e}")
    logger.error("设置排名数据失败，重试次数已用尽")
    return False

async def get_embedding_from_cache(key, querys):
    """
    从缓存中获取embedding
    """
    embeddings = {}
    ranking_data = await get_ranking_key_data(key)
    for items in ranking_data:
        query = items["query"]
        embedding = items.get("emb") or []
        if not embedding:
            continue
        embeddings[query] = embedding
    embeddings = {query: embedding for query, embedding in embeddings.items() if query in querys}
    return embeddings

async def get_text_embedding(texts: str, retry=5):
    """获取文本的 embedding 向量"""
    url = "http://mproxy.search.weibo.com/embedding/vectors"
    post_data = {
        "texts": texts,
        "sid": "smart_ algorithm_sstj",
        "model": "vector",
        "seqid": f"jingxuan_{int(time.time())}",
    }
    headers = {"content-Type": "application/json"}
    timeout = 10
    for i in range(retry):
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(timeout)) as session:
                async with session.post(url=url, json=post_data, headers=headers) as r:
                    ans = await r.json()
                    status = ans["status"]
                    if status != 1000:
                        if i == retry - 1:
                            logger.error(f"fail to get vector , {ans}")
                            return []
                        else:
                            continue
                    return ans["data"]
        except Exception as e:
            if i == retry - 1:
                logger.error(f"fail to get vector , err is {e}")
                return []
    return []


async def get_text_embedding_batch(query_list, max_concurrency=10, batch_size=10):
    """批量获取文本的向量"""
    tasks = []
    semaphore = asyncio.Semaphore(max_concurrency)
    async def embedding_task(querys):
        async with semaphore:
            return await get_text_embedding(querys)

    cur_batch = []
    for query in query_list:
        cur_batch.append(query)
        if len(cur_batch) == batch_size:
            task = asyncio.create_task(embedding_task(cur_batch))
            tasks.append(task)
            cur_batch = []
    if cur_batch:
        task = asyncio.create_task(embedding_task(cur_batch))
        tasks.append(task)
    embedding_res = await asyncio.gather(*tasks)
    embedding_res = [item for sublist in embedding_res for item in sublist]
    return dict(zip(query_list, embedding_res))


async def update_embedding_from_api(embeddings, querys):
    """
    从接口获取embedding
    """
    new_querys = [query for query in querys if query not in embeddings]
    embedding_res = await get_text_embedding_batch(new_querys)
    for query, embedding in embedding_res.items():
        embeddings[query] = embedding
        logger.info(f"query: {query}, emb: {embedding}")
    return embeddings

async def update_embedding_data(key, data):
    """更新embedding数据"""
    querys = [item["query"] for item in data]
    embeddings = await get_embedding_from_cache(key, querys)
    embeddings = await update_embedding_from_api(embeddings, querys)
    for items in data:
        items["emb"] = embeddings[items["query"]]
    return data

