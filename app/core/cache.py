"""离线处理流程"""
# -*- coding:utf-8 -*-
import time
import traceback
import asyncio
from lib.safe_logger import get_logger
from app.core.llm.intervention import INTERVENTION_KEY, get_person_intervention_data
from app.core.llm.wis_search import WIS_SEARCH_KEY, get_wis_search_data
from app.core.llm.hot_question import HOT_QUESTION_KEY, get_hot_question_data
from app.core.llm.article import ARTICLE_KEY, get_article_data
from app.core.llm.utils import get_ranking_key_data, set_ranking_key_data

logger = get_logger("log/wis_stream_query.log", "WIS_STREAM_QUERY")


class MongoStorage:
    def __init__(self):
        # 按照优先级排序，后面会按照优先级query去重
        self.all_keys = {
            INTERVENTION_KEY: get_person_intervention_data,
            WIS_SEARCH_KEY: get_wis_search_data,
            HOT_QUESTION_KEY: get_hot_question_data,
            ARTICLE_KEY: get_article_data,
        }
        self.ranking_data = {"data": {key: [] for key in self.all_keys}}
        self.ranking_keys = [WIS_SEARCH_KEY, HOT_QUESTION_KEY, ARTICLE_KEY]
        self.intervention_keys = [INTERVENTION_KEY]

    def get_ranking_data_cache(self):
        """从缓存中获取排名数据"""
        return self.ranking_data

    def remove_duplicate_query(self):
        """按照优先级去除重复的query"""
        all_query = set()
        log_info = ""
        for key in self.all_keys:
            cur_list = self.ranking_data["data"][key]
            final_list = []
            for item in cur_list:
                query = item["query"]
                if query not in all_query:
                    all_query.add(query)
                    final_list.append(item)
            self.ranking_data["data"][key] = final_list
            log_info += f"key: {key}, len: {len(final_list)}\t"
        logger.info("after remove duplicate query: " + log_info)

    async def no_lock_run_rank_data(self, func_name, rank_keys, sleep_time, false_update=False):
        """没有锁的运行程序"""
        while True:
            try:
                tasks = [get_ranking_key_data(key) for key in rank_keys]
                all_list = await asyncio.gather(*tasks)

                for key, cur_list in zip(rank_keys, all_list):
                    if cur_list or false_update:
                        self.ranking_data["data"][key] = cur_list
                        logger.info(f"{func_name} get_ranking_data {key} success, data_len: {len(cur_list)}")
                    else:
                        logger.info(f"{func_name} get_ranking_data {key} no data")
                self.remove_duplicate_query()
            except Exception as e:
                logger.error(f"{func_name} get_ranking_data error: {e}")
            await asyncio.sleep(sleep_time)

    async def run_rank_data(self, func_name, rank_keys, sleep_time, false_update=False):
        """运行榜单数据"""
        while True:
            try:
                start_time = time.time()
                tasks = [self.all_keys[key]() for key in rank_keys]
                all_list = await asyncio.gather(*tasks)

                tasks = []
                tasks_data = []
                length = 0
                # 遍历所有需要更新的榜单
                for key, cur_list in zip(rank_keys, all_list):
                    if cur_list or false_update:
                        tasks_data.append((key, cur_list))
                        tasks.append(set_ranking_key_data(key, cur_list))
                        logger.info(f"{func_name} | {key} | length: {len(cur_list)}")
                    else:
                        length += len(self.ranking_data["data"][key])
                        logger.warning(f"{func_name} | {key} | error")
                # 保存当前的榜单数据到缓存
                check_list = await asyncio.gather(*tasks)
                for (key, cur_list), check in zip(tasks_data, check_list):
                    if check:
                        self.ranking_data["data"][key] = cur_list
                    else:
                        logger.warning(f"set_ranking_key_data {key} | error")
                    length += len(self.ranking_data["data"][key])
                self.remove_duplicate_query()
                logger.info(f"finally, {func_name} data_length: {length}, cost_time: {time.time() - start_time}")
            except Exception as e:
                logger.error(f"{func_name} run_rank_data | error: {e}, traceback: {traceback.format_exc()}")
            await asyncio.sleep(sleep_time)

    async def run(self):
        """
        有锁运行程序
        """
        tasks = [
            self.run_rank_data("rank-query", self.ranking_keys, 300),
            self.run_rank_data("person-intervention", self.intervention_keys, 60,True),
        ]
        await asyncio.gather(*tasks)
    
    async def no_lock_run(self):
        """没有锁的排行榜数据运行程序"""
        tasks = [
            self.no_lock_run_rank_data("rank-query", self.ranking_keys, 200),
            self.no_lock_run_rank_data("person-intervention", self.intervention_keys, 50),
        ]
        await asyncio.gather(*tasks)

wis_stream_query_mongo = MongoStorage()
