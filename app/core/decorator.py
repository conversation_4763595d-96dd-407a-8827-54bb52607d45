from functools import cache, partial, wraps
import time
from lib.safe_logger import get_logger
logger = get_logger("log/time.log", "Time")



# 函数运行时间装饰器
def calculate_execution_time(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        logger.info(
            f"Function {func.__name__} executed in {execution_time:.4f} seconds"
        )
        return result
    return wrapper

# 异步兼容的装饰器
def calculate_async_execution_time(func):
    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)  # 关键点: 异步等待
        finally:
            end_time = time.time()
            execution_time = end_time - start_time
            logger.info(
                f"Async Function {func.__name__} executed in {execution_time:.4f} seconds"
            )
        return result
    return async_wrapper
