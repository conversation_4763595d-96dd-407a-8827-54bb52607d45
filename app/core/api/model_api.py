"""大模型相关api接口"""
import asyncio
import base64
import hashlib
import hmac
import os
import random
import time
import re
import urllib.parse
from hashlib import sha1
from datetime import datetime
import json

import aiohttp

from app.core.api.base_api import BaseApi
from app.core.api.config import CHATGLM4_9B_URL, QWEN_72B_URL, STREAM_QWEN_72B_URL, EMOTION_URL, DEEPSEEK_R1_URL, \
    STREAM_DEEPSEEK_R1_URL, DEEPSEEK_DEFAULT_APPKEY, DEEPSEEK_APPKEYS, DEEPSEEK_AIGC_URL, \
    XiaoYi_QWEN3_30B_URL, QWEN3_30B_URL, DEEPSEEK_MODEL_ID, DEEPSEEK_DEFAULT_MODEL_ID


def md5_hash(query):
    return hashlib.md5(query.encode()).hexdigest()


def get_bucket_id(md5_str, bucket_num=100):
    # 方法1：将MD5字符串转换为整数后取模
    hash_int = int(md5_str, 16)
    return hash_int % bucket_num


def token_loader(token_file):
    def read_token():
        try:
            with open(token_file, "r") as f:
                return f.readline().strip()
        except FileNotFoundError:
            print(f"token 文件不存在")
            return ""  # 处理文件不存在的情况
        except Exception as e:
            print(f"读取 token 文件出错: {e}")
            return ""

    token_update_time_sec = int(os.path.getmtime(token_file))
    token = read_token()

    def wrapper():
        nonlocal token
        nonlocal token_update_time_sec
        now_sec = int(time.time())
        if now_sec - token_update_time_sec > 3600:
            new_token = read_token()
            if new_token:
                token = new_token
                token_update_time_sec = now_sec
        return token

    return wrapper


load_tauth_token = token_loader(
    r'/data1/minisearch/upload/token/tauth_token_file')


def get_tauth_token(uid):
    token_str = load_tauth_token()
    token_obj = json.loads(token_str)
    tauth_token = token_obj['tauth_token']
    tauth_token_secret = token_obj['tauth_token_secret']
    param = f"uid={uid}"
    sign_hmac = hmac.new(tauth_token_secret.encode(),
                         param.encode(), hashlib.sha1).digest()
    sign_base64 = base64.b64encode(sign_hmac).decode()
    sign = urllib.parse.urlencode({"sign": sign_base64})[5:]
    return {
        r'Authorization': f'TAuth2 token="{urllib.parse.quote(tauth_token)}", param="{urllib.parse.quote(param)}", sign="{sign}"',
        "Content-Type": "application/json"
    }


def count_tokens(weibo, response, model, begin, llm_stage="", first_stream=None):
    if not response:
        return
    query = weibo.get("query", "")
    traceid = weibo.get("traceid", "")
    input_token = response.get("prompt_tokens", 0)
    output_token = response.get("completion_tokens", 0)
    json_data = {
        "query": query,
        "version": traceid,
        "data_type": "zs_call_model",
        "llm_stage": llm_stage,
        "prompt_tokens": input_token,
        "completion_tokens": output_token,
        "model_type": model,
        "in_time_ms": begin,
        "end_process": time.time(),
    }
    if first_stream:
        json_data["first_answer"] = first_stream
    weibo["llm_trace_info"].append(json_data)
    return json_data


class ModelApi(BaseApi):
    """大模型相关api接口"""

    @staticmethod
    async def async_process_res(res):
        """返回数据处理，便于打印信息，此类为json格式返回"""
        return await res.json()

    async def async_chatglm4_9b_api(self, prompt, max_tokens=50, timeout=2, retry=1):
        """chatglm4_9b_api"""
        url = CHATGLM4_9B_URL
        datas = {
            "temperature": "0.1",
            "max_tokens": max_tokens,
            "repetition_penalty": "1.15",
            "messages": ["user", prompt]
        }
        api_name = "CHATGLM4_9B"
        return await self.async_post(url, api_name, datas, timeout=timeout, retry=retry)


class ModelGlm4Wrapper(ModelApi):
    def __init__(self, weibo, pid, pre_log_msg, llm_call_stage):
        self.weibo = weibo
        super().__init__(pid, pre_log_msg)
        self.llm_call_stage = llm_call_stage

    async def async_chatglm4_9b_api(self, prompt, max_tokens=50, timeout=2, retry=1):
        begin = time.time()
        result = await super().async_chatglm4_9b_api(prompt, max_tokens,timeout, retry)
        if result:
            json_data = count_tokens(self.weibo, result, "chatglm4", begin, self.llm_call_stage)
            self.logger.info(self.pre_log_msg + f"count_tokens: {json.dumps(json_data, ensure_ascii=False)}")
        return result


class ModelQwen(BaseApi):

    @staticmethod
    def process_res(res):
        """返回数据处理，便于打印信息，此类为json格式返回"""
        return res.json()

    def call(self, prompt, schema_index=None, max_tokens=2000, temperature=0.1, timeout=200, retry=1):
        """chatglm4_9b_api"""
        url = QWEN_72B_URL
        datas = {
            "temperature": temperature,
            "max_tokens": 2000,
            "rid": "31000",
            "repetition_penalty": 1.15,
            "model": "qwen15-72b-chat",
            "messages": ["system", datetime.now().strftime('当前时间 %Y-%m-%d.'), "user", prompt]
        }
        if schema_index is not None:
            datas.update({"schema_idx": schema_index})

        api_name = "QWEN_72B"
        return self.post(url, api_name, datas, timeout=timeout, retry=retry)

    async def async_call(self, prompt, schema_index=None, max_tokens=2000, temperature=0.1, timeout=200, retry=1):
        """chatglm4_9b_api"""
        url = QWEN_72B_URL
        datas = {
            "temperature": temperature,
            "max_tokens": 2000,
            "rid": "31000",
            "repetition_penalty": 1.15,
            "model": "qwen15-72b-chat",
            "messages": ["system", datetime.now().strftime('当前时间 %Y-%m-%d.'), "user", prompt]
        }
        if schema_index is not None:
            datas.update({"schema_idx": schema_index})

        api_name = "QWEN_72B"
        return await self.async_post(url, api_name, datas, timeout=timeout, retry=retry)

    def stream_call(self, prompt, schema_index=None, max_tokens=2000, temperature=0.1, timeout=200, retry=1):
        """stream_qwen_72b_api"""
        url = STREAM_QWEN_72B_URL
        datas = {
            "temperature": temperature,
            "stream": True,
            "max_tokens": 2000,
            "rid": "31000",
            "repetition_penalty": 1.2,
            "model": "qwen15-72b-chat",
            "messages": ["system", datetime.now().strftime('当前时间 %Y-%m-%d.'), "user", prompt]
        }
        api_name = "STREAM_QWEN_72B"
        return self.stream_post(url, api_name, datas, timeout=timeout, retry=retry)

    async def async_stream_call(self, prompt, schema_index=None, max_tokens=2000, temperature=0.1, timeout=200, retry=1):
        """stream_qwen_72b_api"""
        url = STREAM_QWEN_72B_URL
        datas = {
            "temperature": temperature,
            "stream": True,
            "max_tokens": 2000,
            "rid": "31000",
            "repetition_penalty": 1.2,
            "model": "qwen15-72b-chat",
            "messages": ["system", datetime.now().strftime('当前时间 %Y-%m-%d.'), "user", prompt]
        }
        api_name = "STREAM_QWEN_72B"
        return self.async_stream_post(url, api_name, datas, timeout=timeout, retry=retry)









