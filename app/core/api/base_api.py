"""api接口基类"""
import json
import aiohttp
import requests
import time
from lib.base import Base


class BaseApi(Base):
    """接口基类"""
    default_headers = {'Content-Type': 'application/json'}
    default_get_res = None

    def __init__(self, pid, pre_log_msg):
        super().__init__(pid)
        self.pre_log_msg = pre_log_msg if isinstance(pre_log_msg, str) else ""

    @staticmethod
    def process_res(res):
        """返回数据处理，便于打印信息，默认不处理，直接返回"""
        return res

    def get(self, url, api_name, params=None, headers=None, timeout=None, retry=1):
        """发送get请求，并记录日志"""
        start = time.time()
        if headers is None:
            headers = self.default_headers
        error_info = ""
        for i in range(int(retry)):
            try:
                res = requests.get(url=url, params=params, headers=headers, timeout=timeout)
                if res.status_code == 200:
                    res = self.process_res(res)
                    self.write_log(message=f"api_name:{api_name}\ttry_times:{i+1}\tcost_time:{time.time()-start}\t"
                                           f"params:{params}\theaders:{headers}\ttimeout:{timeout}\tresponse:{res}")
                    return res
            except Exception as e:
                error_info = str(e)
        self.write_log(write_type="ERROR", message=f"api_name:{api_name}\ttry_times:{retry}\t"
                                                   f"cost_time:{time.time()-start}\tparams:{params}\t"
                                                   f"headers:{headers}\ttimeout:{timeout}\terror_info:{error_info}")
        return self.default_get_res

    def post(self, url, api_name, data, headers=None, timeout=None, retry=1):
        """发送post请求，并记录日志"""
        start = time.time()
        if headers is None:
            headers = self.default_headers
        error_info = ""
        for i in range(int(retry)):
            try:
                res = requests.post(url=url, json=data, headers=headers, timeout=timeout)
                if res.status_code == 200:
                    res = self.process_res(res)
                    self.write_log(message=f"api_name:{api_name}\ttry_times:{i + 1}\tcost_time:{time.time()-start}\t"
                                           f"json:{data}\theaders:{headers}\ttimeout:{timeout}\tresponse:{res}")
                    return res
            except Exception as e:
                error_info = str(e)
        self.write_log(write_type="ERROR", message=f"api_name:{api_name}\ttry_times:{retry}\t"
                                                   f"cost_time:{time.time() - start}\tjson:{data}\theaders:{headers}\t"
                                                   f"timeout:{timeout}\terror_info:{error_info}")
        return self.default_get_res

    def stream_post(self, url, api_name, data, headers=None, timeout=None, retry=1):
        """发送post请求，并记录日志"""
        if headers is None:
            headers = self.default_headers
        error_info = ""
        final_result = None
        for i in range(int(retry)):
            try:
                res = requests.post(url=url, json=data, headers=headers, timeout=timeout, stream=True)
                if res.status_code == 200:
                    data = bytearray()
                    for chunk in res.iter_content(4096):
                        if chunk:
                            data.extend(chunk)
                            split_symbol = b'\n'
                            while split_symbol in data:
                                index = data.find(split_symbol)
                                split = data[0:index + 1]
                                data = data[index + 1:]
                                content = split.decode("utf-8")
                                strip_text = content[:len(content) - 1]
                                dict_text = json.loads(strip_text)
                                final_result = dict_text
                                yield dict_text

                    self.logger.info(self.pre_log_msg + f"api_name:{api_name}\tjson:{data}\theaders:{headers}\t"
                                                        f"timeout:{timeout}\ttry_times:{i + 1}\tresponse:{res}")
                    return final_result
            except Exception as e:
                error_info = str(e)
        self.logger.error(self.pre_log_msg + f"api_name:{api_name}\tjson:{data}\theaders:{headers}\ttimeout:{timeout}\t"
                                             f"try_times:{retry}\terror_info:{error_info}")
        return self.default_get_res

    async def async_stream_post(self, url, api_name, data, headers=None, timeout=None, retry=1):
        """协程发送post请求，并记录日志"""
        prompt = data
        start = time.time()
        if headers is None:
            headers = self.default_headers
        error_info = ""
        error_code = 0
        final_result = None
        for i in range(int(retry)):
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=200)) as session:  # 200
                    async with session.post(url, headers=headers, json=data) as res:
                        error_code = res.status
                        if res.status == 200:
                            data = bytearray()
                            async for chunk in res.content.iter_any():
                                if chunk:
                                    data.extend(chunk)
                                    split_symbol = b'\n'
                                    while split_symbol in data:
                                        index = data.find(split_symbol)
                                        split = data[0:index + 1]
                                        data = data[index + 1:]
                                        content = split.decode("utf-8")
                                        strip_text = content[:len(content) - 1]
                                        dict_text = json.loads(strip_text)
                                        final_result = dict_text
                                    next_str = dict_text['text']
                                    if next_str:
                                        yield dict_text
                            self.logger.info(self.pre_log_msg + f"api_name:{api_name}\tjson:{prompt}\theaders:{headers}\t"
                                                            f"timeout:{timeout}\ttry_times:{i + 1}\tres:{final_result}")
            except Exception as e:
                error_info = str(e)
        self.write_log(write_type="INFO", message=f"api_name:{api_name}\ttry_times:{retry}\t"
                                                   f"cost_time:{time.time() - start}\tjson:{prompt}\theaders:{headers}\t"
                                                   f"timeout:{timeout}\terror_info:{error_info}\tres:{error_code}")


    @staticmethod
    async def async_process_res(res):
        """返回数据处理，便于打印信息，默认打印文本"""
        return await res.json()

    async def async_get(self, url, api_name, params=None, headers=None, timeout=None, retry=1):
        """协程发送get请求，并记录日志"""
        start = time.time()
        if headers is None:
            headers = self.default_headers
        error_info = ""
        error_code = 0
        for i in range(int(retry)):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(url=url, params=params, headers=headers, timeout=timeout) as r:
                        error_code = r.status
                        if r.status == 200:
                            res = await self.async_process_res(r)
                            self.write_log(
                                message=f"api_name:{api_name}\ttry_times:{i + 1}\tcost_time:{time.time() - start}\t"
                                        f"params:{params}\theaders:{headers}\ttimeout:{timeout}\tresponse:{res}")
                            return res
            except Exception as e:
                error_info = str(e)
        self.write_log(write_type="ERROR", message=f"api_name:{api_name}\ttry_times:{retry}\t"
                                                   f"cost_time:{time.time() - start}\tparams:{params}\t"
                                                   f"headers:{headers}\ttimeout:{timeout}\terror_info:{error_info}\tresponse:{error_code}")
        return self.default_get_res

    async def async_post(self, url, api_name, data, headers=None, timeout=200, retry=1):
        """协程发送post请求，并记录日志"""
        start = time.time()
        if headers is None:
            headers = self.default_headers
        error_info = ""
        error_code = 0
        response = None
        for i in range(int(retry)):
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(timeout)) as session:
                    async with session.post(url=url, json=data, headers=headers) as r:
                        error_code = r.status
                        if r.status == 200:
                            res = await self.async_process_res(r)
                            self.write_log(
                                message=f"api_name:{api_name}\ttry_times:{i + 1}\tcost_time:{time.time() - start}\t"
                                        f"json:{data}\theaders:{headers}\ttimeout:{timeout}\tresponse:{res}")
                            return res
                        response = r
            except Exception as e:
                error_info = str(e)
        self.write_log(write_type="ERROR", message=f"api_name:{api_name}\ttry_times:{retry}\t"
                                                   f"cost_time:{time.time() - start}\tjson:{data}\theaders:{headers}\t"
                                                   f"timeout:{timeout}\terror_info:{error_info}\tresponse:{error_code}\tmsg:{response}")
        return self.default_get_res
