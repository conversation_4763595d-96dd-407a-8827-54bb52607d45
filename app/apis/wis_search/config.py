"""
WIS搜索模块配置文件
"""

# 响应状态码
class ResponseStatus:
    SUCCESS = 200
    BAD_REQUEST = 400
    INTERNAL_ERROR = 500

# 响应消息
class ResponseMessage:
    SUCCESS = "success"
    INVALID_SCENE = "场景描述不能为空"
    INTERNAL_ERROR = "服务内部错误，请稍后重试"

# 默认响应模板
class ResponseTemplate:
    WITH_TOPIC = "稍等一下，正在为你整理{topic}的完整分析。"
    WITHOUT_TOPIC = "稍等一下，正在为你整理原博的完整分析。"

# 话题清理配置
class TopicConfig:
    INVALID_TOPICS = ["...", "空", "无", "None", "null", ""]
    MAX_TOPIC_LENGTH = 100

# 日志配置
class LogConfig:
    VIEW_LOG_PATH = "log/wis_search_view.log"
    SERVICE_LOG_PATH = "log/llm_api_service.log"
    SIXIN_LOG_PATH = "log/sixin.log"

# API配置
class ApiConfig:
    DEFAULT_TIMEOUT = 200
    DEFAULT_RETRY = 1
    MAX_SCENE_LENGTH = 1000
