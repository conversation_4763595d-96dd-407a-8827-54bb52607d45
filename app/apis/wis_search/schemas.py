
from pydantic import BaseModel, Field
from typing import Optional


class TipRequest(BaseModel):
    """提示请求模型"""
    scene: str = Field(..., description="场景描述", min_length=1, max_length=1000)


class TipResponse(BaseModel):
    """提示响应模型"""
    status: int = Field(default=200, description="状态码")
    data: dict = Field(..., description="响应数据")
    msg: str = Field(default="success", description="响应消息")
