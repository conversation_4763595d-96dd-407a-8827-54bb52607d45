import time
import datetime
import random
from fastapi import APIRouter,Request
from app.apis.wis_search.schemas import TipRequest
from lib.safe_logger import get_logger
from lib.safe_convert import convert_to_int
from app.apis.wis_search.services import LLMApiService

router = APIRouter()
logger = get_logger("log/wis_search_view.log", "VIEW")



@router.post("/api/v1/tips")
async def post_tip(request: TipRequest):
    scene = request.scene
    res = LLMApiService().get_sixin_recommend(scene)
    data = {
        "status": 200,
        "data": {
            "text": res
        },
        "msg": "success"
    }
    return data


