import time
import uuid
from fastapi import APIRouter, HTTPException, status
from fastapi.responses import JSONResponse
from app.apis.wis_search.schemas import TipRequest, TipResponse
from lib.safe_logger import get_logger
from app.apis.wis_search.services import LLMApiService

router = APIRouter()
logger = get_logger("log/wis_search_view.log", "VIEW")


@router.post("/api/v1/tips", response_model=TipResponse)
async def post_tip(request: TipRequest) -> TipResponse:
    """
    获取提示信息接口

    Args:
        request: 包含场景描述的请求体

    Returns:
        TipResponse: 包含提示文本的响应

    Raises:
        HTTPException: 当处理失败时返回500错误
    """
    request_id = str(uuid.uuid4())
    start_time = time.time()

    try:
        logger.info(f"[{request_id}] 接收到提示请求，场景: {request.scene}")

        # 参数验证
        if not request.scene or not request.scene.strip():
            logger.warning(f"[{request_id}] 场景参数为空")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="场景描述不能为空"
            )

        # 调用服务层
        service = LLMApiService()
        result = await service.get_sixin_recommend(request.scene.strip())

        # 构建响应
        response_data = TipResponse(
            status=200,
            data={"text": result or ""},
            msg="success"
        )

        cost_time = time.time() - start_time
        logger.info(f"[{request_id}] 提示请求处理成功，耗时: {cost_time:.3f}s")

        return response_data

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        cost_time = time.time() - start_time
        logger.error(f"[{request_id}] 提示请求处理失败，耗时: {cost_time:.3f}s，错误: {str(e)}")

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务内部错误，请稍后重试"
        )


