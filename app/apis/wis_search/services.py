"""
业务接口
"""
import json
import time
import asyncio
from datetime import datetime, timedelta

import aiohttp

from app.core.decorator import calculate_async_execution_time
from lib.safe_logger import get_logger
from lib.safe_convert import convert_to_int
from app.core.cache import wis_stream_query_mongo
from app.apis.wis_search.utils.common_page import CommonPage,UidListManager
from app.apis.wis_search.utils.first_page import FirstPageWithWhiteList,FirstPageWithoutWhiteList
from app.apis.wis_search.utils.general_page import GeneralPage
from lib.redis_utils import async_redis_client, async_query_data_redis_client

class LLMApiService:
    def __init__(self):
        self.logger = get_logger("log/llm_api_service.log", "LLMApiService")

    def get_sixin_recommend(self, scene):
        return ""
        pass


if __name__ == "__main__":
    async def main():
        wis_search_service = LLMApiService()
        data = await wis_search_service.get_rank_list_v2("1233124",10,1)
        print(data)
        pass
    asyncio.run(main())
