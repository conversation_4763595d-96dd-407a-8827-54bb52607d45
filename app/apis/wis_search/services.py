"""
业务接口
"""
import json
import time
import asyncio
from datetime import datetime, timedelta

from app.core.llm.sixin import SiXin
from lib.safe_logger import get_logger


class LLMApiService:
    def __init__(self):
        self.logger = get_logger("log/llm_api_service.log", "LLMApiService")

    async def get_sixin_recommend(self, scene: str) -> str:
        """
        获取思辨推荐内容

        Args:
            scene: 场景描述

        Returns:
            str: 推荐内容

        Raises:
            Exception: 当调用失败时抛出异常
        """
        try:
            self.logger.info(f"开始获取思辨推荐，场景: {scene}")
            start_time = time.time()

            result = await SiXin().call(scene)

            cost_time = time.time() - start_time
            self.logger.info(f"获取思辨推荐成功，耗时: {cost_time:.3f}s，结果长度: {len(result) if result else 0}")

            return result

        except Exception as e:
            self.logger.error(f"获取思辨推荐失败，场景: {scene}，错误: {str(e)}")
            raise


