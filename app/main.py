"""
    FastAPI 应用程序
"""
import asyncio
import uvicorn
from fastapi import FastAPI
from lib.redis_utils import async_redis_client
from lib.safe_logger import get_logger
from app.routes import init_routers
from app.core.cache import wis_stream_query_mongo

logger = get_logger("log/wis_search_main.log", "MAIN")


class Application(FastAPI):
    """
    FastAPI 应用程序
    """
    def __init__(self):
        super().__init__()

        # 初始化 App
        self.init_app()

    def init_app(self):
        """
        初始化 App
        """
        # 注册中间件 for ..
        # jwt.init_middleware(self)
        # mysql.init_middleware(self)
        # cors.init_middleware(self)

        # 注册路由
        init_routers(self)
        self.dependencies = []


app = Application()

@app.on_event("startup")
async def start_background_tasks():
    """
    启动后台任务
    """
    key = "WIS_SEARCH_FLOW_CACHE_LOCK"
    redis_client = async_redis_client.get_redis_server(key)
    locked = False
    try:
        locked = await redis_client.set(key, 1, ex=60, nx=True)
    except Exception as e:
        logger.error(f"redis set error: {e}")
    if locked:
        logger.info("wis_search_flow is starting")
        asyncio.create_task(wis_stream_query_mongo.run())  # 启动循环任务
    else:
        logger.info("wis_search_flow is already running")
        asyncio.create_task(wis_stream_query_mongo.no_lock_run())  # 启动循环任务

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
