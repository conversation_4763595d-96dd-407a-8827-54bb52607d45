"""
加载应用
"""
import os
import importlib
import traceback
from fastapi import APIRouter, FastAPI
from lib.safe_logger import get_logger

logger = get_logger("log/router.log", "ROUTER")

def router_v1():
    """加载应用"""
    router = APIRouter()

    current_dir = os.path.abspath(os.path.dirname(__file__))
    api_dir = os.path.join(current_dir, 'apis')

    # 动态加载应用
    apps = []
    for _, dirs, _ in os.walk(api_dir):
        for dir_name in dirs:
            if dir_name.startswith('__'):
                continue

            try:
                app = importlib.import_module(f'app.apis.{dir_name}.views')
                router.include_router(app.router, tags=[dir_name])
                apps.append(dir_name)
            except Exception as e:
                logger.error(f'加载应用【{dir_name}】失败，Error: {e} {traceback.format_exc()}')
                continue

        # 只遍历第一层
        break
    logger.info(f'加载的应用: {apps}')

    return router


def init_routers(app: FastAPI):
    """
    初始化路由
    """
    app.include_router(router_v1(), prefix='/api', tags=['v1'])
