APP_NAME=wis_search_flow
DOCKER_NAME=registry.api.weibo.com/wbsearch/wis_search_flow:v1.0

docker rmi $(docker images -f "dangling=true" -q)
echo "pull $DOCKER_NAME"
docker pull $DOCKER_NAME
echo "stop and remove $APP_NAME"
docker stop $APP_NAME
docker rm -fv $APP_NAME
echo "start $APP_NAME"
docker run --restart=always --name $APP_NAME \
-v /data1/minisearch/upload/LLM_TASK/wis_search_flow:/data1/minisearch/upload/LLM_TASK/wis_search_flow \
-p 16868:16868 \
-itd $DOCKER_NAME