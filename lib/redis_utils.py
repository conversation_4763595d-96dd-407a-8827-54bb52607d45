
import os
import hashlib
from ctypes import cdll, c_ulonglong

from redis import asyncio as aioredis

TIME_OUT = 3
HEALTH_CHECK_INTERVAL = 1

curdir = os.path.dirname(os.path.realpath(__file__))
lib_handle = cdll.LoadLibrary(curdir + '/libcrc.so')
RunCRC64 = lib_handle.RunCRC64
RunCRC64.restype = c_ulonglong


class AsyncRedisConnect:

    def __init__(self):
        self.servers = []
        self.hosts =  [
            'pkm26754.eos.grid.sina.com.cn',
            'pkm26755.eos.grid.sina.com.cn',
            'pkm26756.eos.grid.sina.com.cn',
            'pkm26757.eos.grid.sina.com.cn',
            'pkm26758.eos.grid.sina.com.cn',
            'pkm26759.eos.grid.sina.com.cn',
            'pkm26760.eos.grid.sina.com.cn',
            'pkm26761.eos.grid.sina.com.cn'
        ]
        self.ports = [26754, 26755, 26756, 26757, 26758, 26759, 26760, 26761]
        self.load_conn_pools()

    def load_conn_pools(self):
        for i in range(len(self.hosts)):
            redis_client = aioredis.Redis(host=self.hosts[i], port=self.ports[i], socket_timeout=TIME_OUT, decode_responses=True, 
                                          health_check_interval=HEALTH_CHECK_INTERVAL, 
                                          retry_on_timeout=True)
            self.servers.append(redis_client)

    def get_hash_index(self, query):
        md5 = hashlib.md5()
        md5.update(query.encode(encoding='utf-8'))
        m = md5.hexdigest()
        map_key = str(m)[-2:]
        return int(map_key, 16) % 8


    def get_redis_server(self, query):
        index = self.get_hash_index(query)
        return self.servers[index]


class AsyncQueryDataRedisConnect:

    def __init__(self):
        self.servers = []
        self.hosts =  [
            'pkm27543.eos.grid.sina.com.cn',
            'pkm27544.eos.grid.sina.com.cn',
            'pkm27545.eos.grid.sina.com.cn',
            'pkm27546.eos.grid.sina.com.cn',
            'pkm27547.eos.grid.sina.com.cn',
            'pkm27548.eos.grid.sina.com.cn',
            'pkm27549.eos.grid.sina.com.cn',
            'pkm27550.eos.grid.sina.com.cn'
        ]
        self.ports = [27543, 27544, 27545, 27546, 27547, 27548, 27549, 27550]
        self.load_conn_pools()

    def load_conn_pools(self):
        for i in range(len(self.hosts)):
            redis_client = aioredis.Redis(host=self.hosts[i], port=self.ports[i], socket_timeout=TIME_OUT, decode_responses=True,
                                          health_check_interval=HEALTH_CHECK_INTERVAL,
                                          retry_on_timeout=True)
            self.servers.append(redis_client)

    def get_hash_index(self, query):
        md5 = hashlib.md5()
        md5.update(query.encode(encoding='utf-8'))
        m = md5.hexdigest()
        map_key = str(m)[-2:]
        return int(map_key, 16) % 8

    def CRC64MOD(self,key, mod):
        mod = int(mod)
        crc64Code = (lib_handle.RunCRC64(key, len(key), 0) % mod)
        return crc64Code

    def _to_utf_8(self,s):
        if isinstance(s, str):
            s = s.encode('utf-8')
        return s

    def crc64_hash(self,key, num):
        shard = self.CRC64MOD(self._to_utf_8(key), num)
        return shard

    def get_redis_server(self, query):
        # index = self.get_hash_index(query)
        index= self.crc64_hash(query, 8)
        return self.servers[index]

async_redis_client = AsyncRedisConnect()
async_query_data_redis_client = AsyncQueryDataRedisConnect()


async def get_one_related_key(redis_server, pattern="wis_query_*"):
    async for key in redis_server.scan_iter(match=pattern):
        key_type = await redis_server.type(key)
        print(key_type)
        return key  # 返回第一个匹配的 key
    return None 

if __name__ == "__main__":
    import asyncio

    async def main():
        redis_server = async_query_data_redis_client.get_redis_server("wis_query_肖战")
        # await redis_server.set("dasd","1")
        r = await redis_server.hgetall("wis_query_肖战")
        print(r)
        # key = await get_one_related_key(redis_server)
        # print("匹配的 key:", key if key else "无匹配项")

    asyncio.run(main())