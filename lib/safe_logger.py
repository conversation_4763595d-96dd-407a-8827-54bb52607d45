import os
import shutil
import threading
import time
from datetime import timedelta, datetime, date
import logging
from loguru import logger
from loguru._ctime_functions import get_ctime

# 移除所有默认logging处理器
logging.getLogger().handlers = []

logger_rotation = '00:00'  # timedelta(minutes=15)
logger_retention = timedelta(days=4)  # timedelta(hours=1)
logger_folder_format = '%Y-%m-%d'  # '%Y-%m-%d_%H_%M_%S'
logger_format = ("{time:YYYY-MM-DD HH:mm:ss.SSS} "
                 "| {process.id} "
                 "|<lvl>{level:8}</>"
                 "| {module}:{line:4} "
                 "| <cyan>{extra[module_name]}</> "
                 "| <lvl>{message}</>")

logger_pool = dict()
logger_lock = threading.Lock()
logger.remove(handler_id=None)
logger_dir = "log/"


def retention_function(files):
    all_items = os.listdir(logger_dir)
    folders = [item for item in all_items if os.path.isdir(os.path.join(logger_dir, item))]
    current_date = datetime.now()
    for folder in folders:
        try:
            folder_date = datetime.strptime(folder, logger_folder_format)
            if current_date - folder_date > logger_retention:
                shutil.rmtree(os.path.join(logger_dir, folder))
                # shutil.move(os.path.join(logger_dir, folder),
                #             os.path.join('/tmp/', folder + '_' + str(int(time.time()))))
        except Exception as e:
            print(e)


def compression_function(old_path):
    timestamp = get_ctime(old_path)
    created_date = datetime.fromtimestamp(timestamp).strftime(logger_folder_format)
    target_folder = os.path.join(logger_dir, created_date)
    if not os.path.exists(target_folder):
        os.makedirs(target_folder, exist_ok=True)
    try:
        shutil.move(old_path, os.path.join(target_folder, os.path.basename(old_path)))
    except Exception as e:
        print(e)


error_logger_filename = logger_dir + f"error.log"
logger.add(error_logger_filename, level='ERROR', rotation=logger_rotation, retention=retention_function,
           compression=compression_function, encoding='utf-8', format=logger_format, enqueue=True)


def get_logger(logger_filename, module_name, enqueue=True):
    with logger_lock:
        pid = os.getpid()
        logger_filename = logger_filename.rstrip(".log") + f"_{pid}.log"
        key = f"{logger_filename}###{module_name}"
        if key in logger_pool:
            return logger_pool[key]

        logger_instance = logger.bind(module_name=module_name)
        handler_id = logger.add(
            logger_filename,
            level='INFO',
            rotation=logger_rotation,
            retention=retention_function,
            compression=compression_function,
            encoding='utf-8',
            format=logger_format,
            filter=lambda record: record["extra"].get("module_name") == module_name,
            enqueue=enqueue
        )
        logger_pool[key] = logger_instance
        return logger_instance
