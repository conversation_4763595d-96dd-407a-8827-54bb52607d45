# -*- coding:utf-8 -*-
"""为避免接口的类型不对，使用新的函数实现"""

import json
from typing import Any, Callable, TypeVar

T = TypeVar('T')

def safe_convert(convert_fn: Callable[[Any], T], value: Any, default: T) -> T:
    """通用安全转换函数。"""
    try:
        if isinstance(value, str):
            value = value.strip()
        if value is None or value == "":
            return default
        return convert_fn(value)
    except (ValueError, TypeError):
        return default

def convert_to_float(s: Any, default: float = 0.0) -> float:
    """转换为浮点数"""
    return safe_convert(float, s, default)


def convert_to_int(s: Any, default: int = 0) -> int:
    """转换为整数"""
    return safe_convert(int, s, default)


def str_to_json(s: str, default=None):
    """字符串转换为json"""
    return safe_convert(json.loads, s, default)
