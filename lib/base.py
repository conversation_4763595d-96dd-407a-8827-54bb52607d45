# -*- coding:utf-8 -*-
""" 
--------------------------------------------------------------------
@function: 基类，子类继承该类重写方法
@time: 2020-04-22
@author:baoquan3 
@version: 
@modify: 
--------------------------------------------------------------------
"""

import os
import logging
from logging.handlers import TimedRotatingFileHandler
from lib.safe_logger import get_logger


class Base(object):

    def __init__(self, pid):
        self.pid = pid
        self.pre_log_msg = ""
        log_filename = "log/" + self.__class__.__name__.lower() + "-" + str(pid) + ".log"
        # self.logger = self.init_logger(log_filename)
        self.logger = get_logger(log_filename, self.__class__.__name__)

    def run(self, **kwargs):
        raise Exception('sub class no overwrite exception')

    def init_logger(self, logger_filename):
        logger = logging.getLogger(self.__class__.__name__ + str(self.pid))
        logger.setLevel(logging.DEBUG)
        formattler = "[%(asctime)s] %(levelname)s [%(name)s:%(lineno)s] %(message)s"
        fmt = logging.Formatter(formattler)
        # 增加error#
        base_dir = os.path.abspath('.')
        error_logger_filename = base_dir + "/../log/" + "error.log"
        error_handler = logging.FileHandler(error_logger_filename)
        error_handler.setFormatter(fmt)
        error_handler.setLevel(logging.ERROR)

        # handler = TimedRotatingFileHandler_MP(loggerFilename, when='D', interval=1, backupCount=2)
        # 上面的多进程滚动库，每天滚动时，会累加之前的历史数据
        handler = TimedRotatingFileHandler(logger_filename, when='midnight', interval=1, backupCount=2)
        handler.setFormatter(fmt)
        logger.addHandler(handler)
        logger.addHandler(error_handler)
        return logger

    def update_pre_log_msg(self, weibo=None):
        """日志前缀信息，日志信息都应该添加，其他特殊的字段可以在后面添加"""
        if not weibo:
            return
        message = f"pid:{self.pid}\t"
        pre_words = ["query", "traceid", "seqid"]
        for word in pre_words:
            format_str = word + ":{}\t"
            message += format_str.format(weibo.get(word, ""))
        self.pre_log_msg = message

    def write_log(self, write_type="INFO", message=""):
        """写入添加前缀信息的日志信息"""
        write_func = {"TRACE": self.logger.trace, "DEBUG": self.logger.debug, "INFO": self.logger.info,
                      "SUCCESS": self.logger.success, "WARNING": self.logger.warning, "ERROR": self.logger.error,
                      "CRITICAL": self.logger.critical}
        if write_type not in write_func:
            write_type = "INFO"
        write_func[write_type](self.pre_log_msg + message)


class Test(Base):

    def run(self, **kwargs):
        self.logger.info("test logger")


if __name__ == '__main__':
    test = Test(1)
    test.run()
