import configparser
from motor import motor_asyncio
import pymongo


class MongoHelper:
    def __init__(self, section):
        conf_file = "conf/output_mongo.ini"
        self.config = configparser.ConfigParser()
        self.config.read(conf_file, encoding='utf-8')
        self.section = section
        self.mongo_cli = self.init()

    def init(self):
        mongo_cluster = self.config.get(self.section, 'mongo_cluster')
        mongo_cluster = mongo_cluster.split(',')
        mongo_db = self.config.get(self.section, 'mongo_db')
        mongo_collection = self.config.get(self.section, 'mongo_collection')

        client = pymongo.MongoClient(mongo_cluster)
        db = client[mongo_db]
        return db[mongo_collection]

    def write(self, match_id, update_dict):
        flt = {'match_id': match_id}
        self.mongo_cli.update_one(flt, {"$set": update_dict}, upsert=True)

    def read(self, match_id):
        res = self.mongo_cli.find(
            {'match_id': match_id})
        return [i for i in res]

    def read_all(self):
        res = self.mongo_cli.find({})
        return [i for i in res]

    def delete(self, match_id):
        flt = {'match_id': match_id}
        self.mongo_cli.delete_one(flt)

    def index(self):
        for i in ['match_id']:
            self.mongo_cli.create_index([(i, 1)])

    def mongo_client(self):
        return self.mongo_cli

    def find(self, kwargs):
        return self.mongo_cli.find(kwargs)


class AsyncMongoHelper:
    def __init__(self, section):
        conf_file = "conf/output_mongo.ini"
        self.config = configparser.ConfigParser()
        self.config.read(conf_file, encoding='utf-8')
        self.section = section
        self.mongo_cli = self.init()

    def init(self):
        mongo_cluster = self.config.get(self.section, 'mongo_cluster')
        mongo_cluster = mongo_cluster.split(',')
        mongo_db = self.config.get(self.section, 'mongo_db')
        mongo_collection = self.config.get(self.section, 'mongo_collection')

        client = motor_asyncio.AsyncIOMotorClient(
            mongo_cluster,
            maxPoolSize=100,  # 增加最大连接数以应对高并发
            minPoolSize=10,  # 略微增加最小连接数
            maxIdleTimeMS=60000,  # 保持60秒的空闲连接时间
            connectTimeoutMS=5000,  # 增加连接超时时间，提高连接成功率
            socketTimeoutMS=10000,  # 增加套接字超时，适应复杂查询
            serverSelectionTimeoutMS=5000,  # 添加服务器选择超时
            waitQueueTimeoutMS=5000  # 增加等待队列超时
        )
        db = client[mongo_db]
        return db[mongo_collection]

    async def write(self, match_id, update_dict):
        flt = {'match_id': match_id}
        await self.mongo_cli.update_one(flt, {"$set": update_dict}, upsert=True)

    async def write_any(self, flt, update_dict):
        await self.mongo_cli.update_one(flt, {"$set": update_dict}, upsert=True)

    async def read(self, match_id):
        res = self.mongo_cli.find({'match_id': match_id})
        return [i async for i in res]

    async def read_all(self):
        res = self.mongo_cli.find({})
        return [i async for i in res]

    async def delete(self, match_id):
        flt = {'match_id': match_id}
        await self.mongo_cli.delete_one(flt)

    async def index(self):
        for i in ['match_id']:
            await self.mongo_cli.create_index([(i, 1)])

    def mongo_client(self):
        return self.mongo_cli

    async def find(self, kwargs):
        res = self.mongo_cli.find(kwargs)
        return [i async for i in res]
